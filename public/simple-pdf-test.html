<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>简单PDF测试</title>
    <style>
        body { margin: 0; padding: 20px; font-family: Arial, sans-serif; }
        iframe { width: 100%; height: 600px; border: 1px solid #ccc; }
        .info { background: #e7f3ff; padding: 10px; margin: 10px 0; border-radius: 4px; }
        .error { background: #ffe7e7; padding: 10px; margin: 10px 0; border-radius: 4px; color: #d00; }
        .success { background: #e7ffe7; padding: 10px; margin: 10px 0; border-radius: 4px; color: #080; }
    </style>
</head>
<body>
    <h1>PDF.js 3.11.174 简单测试</h1>
    
    <div class="info">
        <strong>测试说明:</strong> 这个页面直接加载PDF.js查看器，用于验证跨域问题是否已解决。
    </div>
    
    <div id="messages"></div>
    
    <h2>直接加载PDF查看器</h2>
    <iframe src="pdfjs-3.11.174/web/viewer.html" id="pdfViewer"></iframe>
    
    <script>
        const messagesDiv = document.getElementById('messages');
        
        function addMessage(type, message) {
            const div = document.createElement('div');
            div.className = type;
            div.innerHTML = `<strong>${new Date().toLocaleTimeString()}:</strong> ${message}`;
            messagesDiv.appendChild(div);
        }
        
        // 监听iframe加载
        document.getElementById('pdfViewer').onload = function() {
            addMessage('success', 'PDF查看器iframe加载成功');
        };
        
        document.getElementById('pdfViewer').onerror = function(e) {
            addMessage('error', 'PDF查看器iframe加载失败: ' + e.message);
        };
        
        // 监听来自PDF.js的消息
        window.addEventListener('message', function(e) {
            console.log('收到PDF.js消息:', e.data);
            if (e.data.type === 'pdfLoaded') {
                addMessage('success', 'PDF文档加载成功');
            } else if (e.data.type === 'pdfLoadError') {
                addMessage('error', 'PDF文档加载失败: ' + (e.data.data?.error || '未知错误'));
            }
        });
        
        // 监听全局错误
        window.addEventListener('error', function(e) {
            if (e.message.includes('file origin') || e.message.includes('PDF')) {
                addMessage('error', '页面错误: ' + e.message);
            }
        });
        
        // 重写console.error来捕获PDF.js的错误
        const originalConsoleError = console.error;
        console.error = function(...args) {
            originalConsoleError.apply(console, args);
            const errorMsg = args.join(' ');
            if (errorMsg.includes('file origin') || errorMsg.includes('PDF')) {
                addMessage('error', '控制台错误: ' + errorMsg);
            }
        };
        
        addMessage('info', '页面初始化完成，开始加载PDF查看器...');
    </script>
</body>
</html>

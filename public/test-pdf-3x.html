<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>PDF.js 3.x 版本测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-result { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        iframe { width: 100%; height: 500px; border: 1px solid #ccc; margin: 10px 0; }
        .test-section { margin: 20px 0; }
        button { padding: 8px 16px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #0056b3; }
    </style>
</head>
<body>
    <h1>PDF.js 3.11.174 版本测试</h1>
    
    <div id="info" class="test-result info">
        <strong>测试信息:</strong><br>
        PDF.js 版本: 3.11.174<br>
        浏览器: <span id="browser-info"></span><br>
        测试时间: <span id="test-time"></span>
    </div>
    
    <div class="test-section">
        <h2>测试1: 默认页面加载</h2>
        <button onclick="loadTest1()">加载测试</button>
        <div id="test1-result"></div>
        <div id="test1-container"></div>
    </div>
    
    <div class="test-section">
        <h2>测试2: 示例PDF文件</h2>
        <button onclick="loadTest2()">加载测试</button>
        <div id="test2-result"></div>
        <div id="test2-container"></div>
    </div>
    
    <div class="test-section">
        <h2>测试3: Base64 PDF测试</h2>
        <button onclick="loadTest3()">加载Base64测试</button>
        <div id="test3-result"></div>
        <div id="test3-container"></div>
    </div>
    
    <script>
        // 初始化页面信息
        document.getElementById('browser-info').textContent = navigator.userAgent;
        document.getElementById('test-time').textContent = new Date().toLocaleString();
        
        let messageCount = 0;
        
        // 监听来自PDF.js的消息
        window.addEventListener('message', function(e) {
            messageCount++;
            console.log(`消息 ${messageCount}:`, e.data);
            
            if (e.data.type === 'pdfLoaded') {
                showResult(e.data.testId || 'unknown', 'success', 'PDF加载成功！');
            } else if (e.data.type === 'pdfLoadError') {
                showResult(e.data.testId || 'unknown', 'error', 'PDF加载失败: ' + (e.data.data?.error || '未知错误'));
            }
        });
        
        function showResult(testId, type, message) {
            const resultDiv = document.getElementById(testId + '-result');
            if (resultDiv) {
                resultDiv.innerHTML = `<div class="test-result ${type}">${message}</div>`;
            }
        }
        
        function createIframe(src, containerId) {
            const container = document.getElementById(containerId);
            container.innerHTML = ''; // 清空容器
            
            const iframe = document.createElement('iframe');
            iframe.src = src;
            iframe.onload = function() {
                console.log('iframe加载完成:', src);
            };
            iframe.onerror = function(e) {
                console.error('iframe加载失败:', src, e);
                showResult(containerId.replace('-container', ''), 'error', 'iframe加载失败');
            };
            
            container.appendChild(iframe);
        }
        
        function loadTest1() {
            showResult('test1', 'info', '正在加载默认页面...');
            createIframe('pdfjs-3.11.174/web/viewer.html', 'test1-container');
        }
        
        function loadTest2() {
            showResult('test2', 'info', '正在加载示例PDF...');
            createIframe('pdfjs-3.11.174/web/viewer.html?file=compressed.tracemonkey-pldi-09.pdf', 'test2-container');
        }
        
        function loadTest3() {
            showResult('test3', 'info', '正在测试Base64加载...');
            
            // 设置一个简单的Base64 PDF数据（这是一个最小的PDF文件）
            const minimalPdfBase64 = 'JVBERi0xLjQKMSAwIG9iago8PAovVHlwZSAvQ2F0YWxvZwovUGFnZXMgMiAwIFIKPj4KZW5kb2JqCjIgMCBvYmoKPDwKL1R5cGUgL1BhZ2VzCi9LaWRzIFszIDAgUl0KL0NvdW50IDEKL01lZGlhQm94IFswIDAgNjEyIDc5Ml0KPj4KZW5kb2JqCjMgMCBvYmoKPDwKL1R5cGUgL1BhZ2UKL1BhcmVudCAyIDAgUgovUmVzb3VyY2VzIDw8Ci9Gb250IDw8Ci9GMSA0IDAgUgo+Pgo+PgovQ29udGVudHMgNSAwIFIKPj4KZW5kb2JqCjQgMCBvYmoKPDwKL1R5cGUgL0ZvbnQKL1N1YnR5cGUgL1R5cGUxCi9CYXNlRm9udCAvSGVsdmV0aWNhCj4+CmVuZG9iago1IDAgb2JqCjw8Ci9MZW5ndGggNDQKPj4Kc3RyZWFtCkJUCi9GMSAxMiBUZgoxMDAgNzAwIFRkCihIZWxsbyBXb3JsZCkgVGoKRVQKZW5kc3RyZWFtCmVuZG9iagp4cmVmCjAgNgowMDAwMDAwMDAwIDY1NTM1IGYgCjAwMDAwMDAwMDkgMDAwMDAgbiAKMDAwMDAwMDA1OCAwMDAwMCBuIAowMDAwMDAwMTE1IDAwMDAwIG4gCjAwMDAwMDAyNDUgMDAwMDAgbiAKMDAwMDAwMDMxMiAwMDAwMCBuIAp0cmFpbGVyCjw8Ci9TaXplIDYKL1Jvb3QgMSAwIFIKPj4Kc3RhcnR4cmVmCjQwNQolJUVPRg==';
            
            // 设置sessionStorage
            sessionStorage.setItem('defaultBase64', minimalPdfBase64);
            
            createIframe('pdfjs-3.11.174/web/viewer.html', 'test3-container');
        }
        
        // 监听错误
        window.addEventListener('error', function(e) {
            console.error('页面错误:', e);
        });
    </script>
</body>
</html>

export default [
  {
    path: '/common/idConfirm',
    name: 'idConfirm',
    component: () =>
      import(/* webpackChunkName: "common" */ '@/views/common/idConfirm.vue'),
    meta: {
      title: '重置密码'
    }
  },
  {
    path: '/common/phoneConfirm',
    name: 'phoneConfirm',
    component: () =>
      import(
        /* webpackChunkName: "common" */ '@/views/common/phoneConfirm.vue'
      ),
    meta: {
      title: '手机号短信验证'
    }
  },
  {
    path: '/common/fundAccountConfirm',
    name: 'fundAccountConfirm',
    component: () =>
      import(
        /* webpackChunkName: "common" */ '@/views/common/fundAccountConfirm.vue'
      ),
    meta: {
      title: '输入身份证信息'
    }
  },
  {
    path: '/common/fundAccountPhoneConfirm',
    name: 'fundAccountPhoneConfirm',
    component: () =>
      import(
        /* webpackChunkName: "common" */ '@/views/common/fundAccountPhoneConfirm.vue'
      ),
    meta: {
      title: '手机号校验'
    }
  },
  {
    path: '/common/fundAccount',
    name: 'fundAccount',
    component: () =>
      import(/* webpackChunkName: "common" */ '@/views/common/fundAccount.vue'),
    meta: {
      title: '结果页'
    }
  },
  {
    path: '/common/riskResult',
    name: 'riskResult',
    component: () =>
      import(/* webpackChunkName: "common" */ '@/views/common/riskResult.vue'),
    meta: {
      title: '风险测评'
    }
  },
  {
    path: '/common/questionVisitList',
    name: 'questionVisitList',
    component: () =>
      import(
        /* webpackChunkName: "common" */ '@/views/common/questionVisitList.vue'
      ),
    meta: {
      title: '问卷回访'
    }
  },
  {
    path: '/common/questionVisitDetail',
    name: 'questionVisitDetail',
    component: () =>
      import(
        /* webpackChunkName: "common" */ '@/views/common/questionVisitDetail.vue'
      ),
    meta: {
      title: '问卷回访'
    }
  },
  {
    path: '/common/idVerify',
    name: 'idVerify',
    component: () =>
      import(/* webpackChunkName: "common" */ '@/views/common/idVerify.vue'),
    meta: {
      title: '身份信息校验'
    }
  },
  {
    path: '/addShareHoldAccount',
    name: 'addShareHoldAccount',
    component: () =>
      import(
        /* webpackChunkName: "common" */ '@/views/common/addShareHoldAccount.vue'
      ),
    meta: {
      title: '补开股东户'
    }
  },
  {
    path: '/inExchangFoundAccount',
    name: 'inExchangFoundAccount',
    component: () =>
      import(
        /* webpackChunkName: "common" */ '@/views/common/inExchangFoundAccount.vue'
      ),
    meta: {
      title: '场内基金开户'
    }
  },
  {
    path: '/changeShareAccountTips',
    name: 'changeShareAccountTips',
    component: () =>
      import(
        /* webpackChunkName: "common" */ '@/views/common/changeShareAccountTips.vue'
      ),
    meta: {
      title: '变更股东户'
    }
  },
  {
    path: '/sjspwd',
    name: 'sjspwd',
    component: () =>
      import(/* webpackChunkName: "common" */ '@/views/sjspwd/sjspwd.vue'),
    meta: {
      title: '申领密码'
    }
  },
  {
    path: '/yyxhResult',
    name: 'yyxhResult',
    component: () =>
      import(/* webpackChunkName: "common" */ '@/views/common/yyxhResult.vue'),
    meta: {
      title: '预约销户结果页'
    }
  },
  {
    path: '/bcPreBizList',
    name: 'bcPreBizList',
    component: () =>
      import(
        /* webpackChunkName: "common" */ '@/views/common/bcPreBizList.vue'
      ),
    meta: {
      title: '预约业务'
    }
  },
  {
    path: '/bcPreBizList/:bizType(.*)',
    name: 'bcPreBizList',
    component: () =>
      import(
        /* webpackChunkName: "common" */ '@/views/common/bcPreBizList.vue'
      ),
    meta: {
      title: '预约业务'
    }
  },
  {
    path: '/browserTips',
    name: 'browserTips',
    component: () =>
      import(/* webpackChunkName: "common" */ '@/views/common/browserTips.vue'),
    meta: {
      title: ''
    }
  },
  {
    path: '/showCreditQuota',
    name: 'showCreditQuota',
    component: () =>
      import(
        /* webpackChunkName: "common" */ '@/views/glxyed/showCreditQuota.vue'
      ),
    meta: {
      title: '管理信用额度'
    }
  },
  {
    path: '/lrIntroduce',
    name: 'lrIntroduce',
    component: () =>
      import(
        /* webpackChunkName: "common" */ '@/views/lrIntroduce/lrIntroduce.vue'
      ),
    meta: {
      title: '两融预约开户'
    }
  },
  {
    path: '/common/fundAccRetrieve',
    name: 'fundAccRetrieve',
    component: () =>
      import(
        /* webpackChunkName: "common" */ '@/views/common/fundAccRetrieve/index.vue'
      ),
    meta: {
      title: '找回资金账号'
    }
  },
  {
    path: '/fundAccRetrieve/phoneConfirm',
    name: 'fundAccRetPhoneConfirm',
    component: () =>
      import(
        /* webpackChunkName: "common" */ '@/views/common/fundAccRetrieve/phoneConfirm.vue'
      ),
    meta: {
      title: '手机号短信验证'
    }
  },
  {
    path: '/common/creditAccountGuide',
    name: 'creditAccountGuide',
    component: () =>
      import(
        /* webpackChunkName: "common" */ '@/views/common/creditAccountGuide.vue'
      ),
    meta: {
      title: ''
    }
  },
  {
    path: '/common/pickSeparatePwd',
    name: 'pickSeparatePwd',
    component: () =>
      import(
        /* webpackChunkName: "common" */ '@/views/common/pickSeparatePwd.vue'
      ),
    meta: {
      title: '启用独立密码'
    }
  },
  {
    path: '/optionRiskNotice',
    name: 'optionRiskNotice',
    component: () => import('@/views/common/optionRiskNotice.vue'),
    meta: {
      title: '期权交易法律与风险告知书'
    },
  },
  {
    path: '/common/reserveMargin',
    name: 'reserveMargin',
    component: () => import('@/views/common/reserveMargin.vue'),
    meta: {
      title: '两融预约开户'
    }
  }
];

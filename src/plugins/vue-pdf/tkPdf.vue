<template>
  <div style="width: 100%; height: 100%; position: relative">
    <!-- loading层 -->
    <div v-if="isLoading" class="pdf-loading">
      <div class="loading-spinner"></div>
      <div class="loading-text">PDF加载中...</div>
    </div>
    <!-- pdf js中viewer.html嵌入iframe中 -->
    <iframe
      ref="pdBox"
      :src="pdfSrc"
      :style="{ opacity: isLoading ? 0 : 1 }"
    ></iframe>
  </div>
</template>
<script>
export default {
  props: {
    src: {
      type: String,
      default: ''
    },
    base64: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      pdfSrc: null,
      isLoading: true
    };
  },
  created() {
    // 添加消息监听器
    window.addEventListener('message', this.handlePdfMessage);

    if (this.base64 !== '') {
      sessionStorage.setItem('defaultBase64', this.base64);
      this.pdfSrc = `${window.location.origin}${process.env.BASE_URL}pdfjs-5.3.93/web/viewer.html`;
    } else {
      this.pdfSrc = `${window.location.origin}${
        process.env.BASE_URL
      }pdfjs-5.3.93/web/viewer.html?v=${new Date().getTime()}&file=${encodeURIComponent(
        this.src
      )}#page=1`;
    }
  },
  methods: {
    handlePdfMessage(event) {
      // 确保消息来源是PDF.js iframe
      if (event.source === this.$refs.pdBox?.contentWindow) {
        if (event.data.type === 'pdfLoaded') {
          console.log('PDF document loaded successfully');
          this.isLoading = false;
        } else if (event.data.type === 'pdfLoadError') {
          console.error('PDF加载失败:', event.data.data?.error || '未知错误');
          this.isLoading = false;
        }
      }
    }
  },
  destroyed() {
    // 移除消息监听器
    window.removeEventListener('message', this.handlePdfMessage);
    sessionStorage.removeItem('defaultBase64');
    this.pdfSrc = null;
  }
};
</script>
<style scoped>
iframe {
  width: 100%;
  height: 100%;
  border: 0px;
  transition: opacity 0.3s ease;
}

.pdf-loading {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: #f5f5f5;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 10;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #e0e0e0;
  border-top: 4px solid #1890ff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

.loading-text {
  color: #666;
  font-size: 14px;
  font-weight: 500;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>

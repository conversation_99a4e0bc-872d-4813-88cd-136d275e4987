module.exports = {
  root: true,
  env: {
    browser: true,
    node: true,
    es6: true
  },
  extends: [
    'plugin:vue/essential',
    'plugin:prettier/recommended',
    'eslint:recommended'
  ],
  rules: {
    'no-console': process.env.NODE_ENV === 'production' ? 'error' : 'off',
    'no-debugger': process.env.NODE_ENV === 'production' ? 'error' : 'off',
    'vue/require-default-prop': 'error',
    'vue/no-v-html': 'off',
    'no-unused-vars': 'error',
    'no-undef': 'error',
    'vue/no-unused-components': 'error',
    'vue/multi-word-component-names': 'off',
    'prettier/prettier': 'error',
    // 添加以下规则提高代码质量
    'no-var': 'error', // 禁用 var，使用 let/const
  },
  parserOptions: {
    parser: '@babel/eslint-parser',
    ecmaVersion: 12,
    sourceType: 'module'
  },
  globals: {
    _hvueToast: 'writable',
    _hvueAlert: 'writable',
    _hvueLoading: 'writable',
    _hvueConfirm: 'writable',
    TKFlowEngine: 'writable',
    TChatRTC: 'writable',
    ActiveXObject: 'writable',
    createScriptElement: 'writable',
    PACK_ENV: 'writable',
    BUILD_HASH: 'writable',
    $h: 'writable',
    $hvue: 'writable',
    serviceOptions: 'writable',
    MODULE_NAME: 'writable',
  },
  plugins: ['prettier'],
  overrides: [
    {
      files: ['**/__tests__/*.{j,t}s?(x)'],
      env: {
        mocha: true
      }
    }
  ]
};
